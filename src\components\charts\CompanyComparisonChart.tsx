import React from 'react';
import { Card, Typography, Space, Radio } from 'antd';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip, ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import type { Company, FinancialData } from '../../types';

const { Title, Text } = Typography;

interface CompanyComparisonChartProps {
  companies: Company[];
  financialData: FinancialData[];
  title?: string;
  metric?: 'revenue' | 'profit' | 'employees';
  type?: 'bar' | 'pie';
  height?: number;
  loading?: boolean;
}

const CompanyComparisonChart: React.FC<CompanyComparisonChartProps> = ({
  companies,
  financialData,
  title = 'Comparação entre Empresas',
  metric = 'revenue',
  type = 'bar',
  height = 300,
  loading = false
}) => {
  const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const getMetricLabel = () => {
    switch (metric) {
      case 'revenue':
        return 'Receita';
      case 'profit':
        return 'Lucro';
      case 'employees':
        return 'Funcionários';
      default:
        return 'Receita';
    }
  };

  const getChartData = () => {
    return companies.map((company, index) => {
      let value = 0;
      
      if (metric === 'employees') {
        value = company.employees;
      } else {
        // Get latest financial data for each company
        const latestData = financialData
          .filter(data => data.companyId === company.id)
          .sort((a, b) => b.period.localeCompare(a.period))[0];
        
        if (latestData) {
          value = metric === 'revenue' ? latestData.revenue : latestData.profit;
        }
      }

      return {
        name: company.name,
        value,
        industry: company.industry,
        color: COLORS[index % COLORS.length]
      };
    });
  };

  const chartData = getChartData();

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{
          backgroundColor: 'white',
          padding: '12px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <Text strong>{data.name}</Text>
          <div style={{ marginTop: '4px' }}>
            <Text>Setor: {data.industry}</Text>
          </div>
          <div style={{ marginTop: '4px' }}>
            <Text style={{ color: data.color }}>
              {getMetricLabel()}: {
                metric === 'employees' 
                  ? `${data.value} funcionários`
                  : formatCurrency(data.value)
              }
            </Text>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderBarChart = () => (
    <BarChart data={chartData}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis 
        dataKey="name" 
        tick={{ fontSize: 12 }}
        angle={-45}
        textAnchor="end"
        height={80}
      />
      <YAxis 
        tick={{ fontSize: 12 }}
        tickFormatter={metric === 'employees' ? undefined : formatCurrency}
      />
      <Tooltip content={<CustomTooltip />} />
      <Bar 
        dataKey="value" 
        name={getMetricLabel()}
        radius={[4, 4, 0, 0]}
      >
        {chartData.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={entry.color} />
        ))}
      </Bar>
    </BarChart>
  );

  const renderPieChart = () => (
    <PieChart>
      <Pie
        data={chartData}
        cx="50%"
        cy="50%"
        labelLine={false}
        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
        outerRadius={80}
        fill="#8884d8"
        dataKey="value"
      >
        {chartData.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={entry.color} />
        ))}
      </Pie>
      <Tooltip content={<CustomTooltip />} />
    </PieChart>
  );

  return (
    <Card
      loading={loading}
      title={
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>
            {title}
          </Title>
          <Space>
            <Radio.Group
              size="small"
              value={metric}
              buttonStyle="solid"
            >
              <Radio.Button value="revenue">Receita</Radio.Button>
              <Radio.Button value="profit">Lucro</Radio.Button>
              <Radio.Button value="employees">Funcionários</Radio.Button>
            </Radio.Group>
          </Space>
        </Space>
      }
      style={{
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}
    >
      <ResponsiveContainer width="100%" height={height}>
        {type === 'pie' ? renderPieChart() : renderBarChart()}
      </ResponsiveContainer>
      
      <div style={{ marginTop: '16px' }}>
        <Space wrap>
          {chartData.map((item, index) => (
            <Space key={index} size="small">
              <div
                style={{
                  width: 12,
                  height: 12,
                  backgroundColor: item.color,
                  borderRadius: '2px'
                }}
              />
              <Text style={{ fontSize: '12px' }}>
                {item.name} ({item.industry})
              </Text>
            </Space>
          ))}
        </Space>
      </div>
    </Card>
  );
};

export default CompanyComparisonChart;
