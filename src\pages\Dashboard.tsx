import React from 'react';
import { Row, Col, Typography, Space, Card, Statistic, Alert } from 'antd';
import { 
  DollarOutlined, 
  TeamOutlined, 
  RiseOutlined, 
  TrophyOutlined 
} from '@ant-design/icons';
import { useTenant } from '../contexts/TenantContext';
import KPICard from '../components/widgets/KPICard';
import RevenueChart from '../components/charts/RevenueChart';
import CompanyComparisonChart from '../components/charts/CompanyComparisonChart';
import { mockFinancialData, mockKPIs } from '../data/mockData';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { currentCompany, companies, isLoading, error } = useTenant();

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Title level={3}>Carregando dashboard...</Title>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Erro ao carregar dados"
        description={error}
        type="error"
        showIcon
        style={{ margin: '20px 0' }}
      />
    );
  }

  if (!currentCompany) {
    return (
      <Alert
        message="Nenhuma empresa selecionada"
        description="Por favor, selecione uma empresa para visualizar o dashboard."
        type="warning"
        showIcon
        style={{ margin: '20px 0' }}
      />
    );
  }

  // Get financial data for current company
  const companyFinancialData = mockFinancialData.filter(
    data => data.companyId === currentCompany.id
  );

  // Get KPIs for current company
  const companyKPIs = mockKPIs[currentCompany.id] || [];

  // Calculate summary statistics
  const latestFinancialData = companyFinancialData
    .sort((a, b) => b.period.localeCompare(a.period))[0];

  const totalRevenue = companyFinancialData.reduce((sum, data) => sum + data.revenue, 0);
  const totalProfit = companyFinancialData.reduce((sum, data) => sum + data.profit, 0);
  const avgProfitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

  return (
    <div>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          Dashboard - {currentCompany.name}
        </Title>
        <Text type="secondary">
          {currentCompany.industry} • {currentCompany.employees} funcionários • 
          Fundada em {currentCompany.foundedYear}
        </Text>
      </div>

      {/* Quick Stats */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Receita Total (3 meses)"
              value={totalRevenue}
              precision={0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<DollarOutlined />}
              formatter={(value) => 
                new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  minimumFractionDigits: 0,
                }).format(Number(value))
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Lucro Total (3 meses)"
              value={totalProfit}
              precision={0}
              valueStyle={{ color: '#52c41a' }}
              prefix={<RiseOutlined />}
              formatter={(value) => 
                new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  minimumFractionDigits: 0,
                }).format(Number(value))
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Margem de Lucro Média"
              value={avgProfitMargin}
              precision={1}
              valueStyle={{ color: '#faad14' }}
              prefix={<TrophyOutlined />}
              suffix="%"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Funcionários"
              value={currentCompany.employees}
              valueStyle={{ color: '#722ed1' }}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* KPI Cards */}
      {companyKPIs.length > 0 && (
        <>
          <Title level={3} style={{ marginBottom: '16px' }}>
            Indicadores Chave de Performance
          </Title>
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            {companyKPIs.map((kpi) => (
              <Col xs={24} sm={12} lg={6} key={kpi.id}>
                <KPICard kpi={kpi} />
              </Col>
            ))}
          </Row>
        </>
      )}

      {/* Charts */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={16}>
          <RevenueChart
            data={companyFinancialData}
            title={`Evolução Financeira - ${currentCompany.name}`}
            height={400}
          />
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title="Informações da Empresa"
            style={{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              height: '100%'
            }}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Text strong>Setor:</Text>
                <br />
                <Text>{currentCompany.industry}</Text>
              </div>
              <div>
                <Text strong>Localização:</Text>
                <br />
                <Text>
                  {currentCompany.address.city}, {currentCompany.address.state}
                </Text>
              </div>
              <div>
                <Text strong>Contato:</Text>
                <br />
                <Text>{currentCompany.contact.email}</Text>
                <br />
                <Text>{currentCompany.contact.phone}</Text>
              </div>
              <div>
                <Text strong>Website:</Text>
                <br />
                <Text>{currentCompany.contact.website || 'Não informado'}</Text>
              </div>
              <div>
                <Text strong>Status:</Text>
                <br />
                <Text style={{ 
                  color: currentCompany.status === 'active' ? '#52c41a' : '#faad14' 
                }}>
                  {currentCompany.status === 'active' ? 'Ativa' : 'Inativa'}
                </Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Company Comparison */}
      <Row gutter={[16, 16]}>
        <Col xs={24}>
          <CompanyComparisonChart
            companies={companies}
            financialData={mockFinancialData}
            title="Comparação entre Todas as Empresas"
            height={350}
          />
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
