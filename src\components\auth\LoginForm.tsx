import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, Space, Alert } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface LoginFormProps {
  onLogin: (credentials: { email: string; password: string }) => Promise<void>;
  loading?: boolean;
  error?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ onLogin, loading = false, error }) => {
  const [form] = Form.useForm();

  const handleSubmit = async (values: { email: string; password: string }) => {
    try {
      await onLogin(values);
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card 
        style={{ 
          width: 400, 
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
          borderRadius: '12px'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
          <div>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              High Capital BI
            </Title>
            <Text type="secondary">
              Dashboard Multi-Tenant
            </Text>
          </div>

          {error && (
            <Alert
              message="Erro de Login"
              description={error}
              type="error"
              showIcon
              closable
            />
          )}

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
            initialValues={{
              email: '<EMAIL>',
              password: 'demo123'
            }}
          >
            <Form.Item
              name="email"
              label="Email"
              rules={[
                { required: true, message: 'Por favor, insira seu email!' },
                { type: 'email', message: 'Email inválido!' }
              ]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="<EMAIL>"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="Senha"
              rules={[
                { required: true, message: 'Por favor, insira sua senha!' },
                { min: 6, message: 'Senha deve ter pelo menos 6 caracteres!' }
              ]}
            >
              <Input.Password 
                prefix={<LockOutlined />} 
                placeholder="Sua senha"
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={loading}
                icon={<LoginOutlined />}
                block
                style={{ height: '45px', fontSize: '16px' }}
              >
                Entrar
              </Button>
            </Form.Item>
          </Form>

          <div style={{ textAlign: 'center', marginTop: '20px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Credenciais de demonstração já preenchidas
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default LoginForm;
