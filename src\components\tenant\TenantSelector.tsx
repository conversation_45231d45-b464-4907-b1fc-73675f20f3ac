/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { Select, Avatar, Space, Typography, Tag } from 'antd';
import { BuildOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useTenant } from '../../contexts/TenantContext';
import type { Company } from '../../types';

const { Text } = Typography;
const { Option } = Select;

interface TenantSelectorProps {
  style?: React.CSSProperties;
  size?: 'small' | 'middle' | 'large';
  showCompanyInfo?: boolean;
}

const TenantSelector: React.FC<TenantSelectorProps> = ({ 
  style, 
  size = 'middle',
  showCompanyInfo = false 
}) => {
  const { currentCompany, companies, switchCompany, isLoading } = useTenant();

  const handleCompanyChange = (companyId: string) => {
    switchCompany(companyId);
  };

  const getIndustryColor = (industry: string): string => {
    const colors: Record<string, string> = {
      'Technology': 'blue',
      'Renewable Energy': 'green',
      'Financial Services': 'gold',
      'Retail': 'purple',
      'Healthcare': 'red',
      'Manufacturing': 'orange',
      'Education': 'cyan'
    };
    return colors[industry] || 'default';
  };

  const renderCompanyOption = (company: Company) => (
    <Option key={company.id} value={company.id}>
      <Space>
        <Avatar 
          size="small" 
          icon={<BuildOutlined />}
          style={{ backgroundColor: '#1890ff' }}
        />
        <div>
          <div style={{ fontWeight: 500 }}>{company.name}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {company.industry} • {company.employees} funcionários
          </Text>
        </div>
        {company.status === 'active' && (
          <CheckCircleOutlined style={{ color: '#52c41a' }} />
        )}
      </Space>
    </Option>
  );

  if (isLoading) {
    return (
      <Select
        loading
        placeholder="Carregando empresas..."
        style={{ minWidth: 200, ...style }}
        size={size}
      />
    );
  }

  return (
    <div>
      <Select
        value={currentCompany?.id}
        onChange={handleCompanyChange}
        placeholder="Selecione uma empresa"
        style={{ minWidth: 250, ...style }}
        size={size}
        optionLabelProp="label"
        dropdownStyle={{ minWidth: 300 }}
      >
        {companies.map(company => (
          <Option 
            key={company.id} 
            value={company.id}
            label={
              <Space>
                <Avatar 
                  size="small" 
                  icon={<BuildOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
                {company.name}
              </Space>
            }
          >
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Space>
                <Avatar 
                  size="small" 
                  icon={<BuildOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
                <div>
                  <Text strong>{company.name}</Text>
                  {company.status === 'active' && (
                    <CheckCircleOutlined 
                      style={{ color: '#52c41a', marginLeft: 8 }} 
                    />
                  )}
                </div>
              </Space>
              <Space>
                <Tag color={getIndustryColor(company.industry)}>
                  {company.industry}
                </Tag>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {company.employees} funcionários
                </Text>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Fundada em {company.foundedYear}
                </Text>
              </Space>
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {company.address.city}, {company.address.state}
              </Text>
            </Space>
          </Option>
        ))}
      </Select>

      {showCompanyInfo && currentCompany && (
        <div style={{ marginTop: 8 }}>
          <Space direction="vertical" size="small">
            <Space>
              <Tag color={getIndustryColor(currentCompany.industry)}>
                {currentCompany.industry}
              </Tag>
              <Text type="secondary">
                {currentCompany.employees} funcionários
              </Text>
            </Space>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {currentCompany.address.city}, {currentCompany.address.state}
            </Text>
          </Space>
        </div>
      )}
    </div>
  );
};

export default TenantSelector;
