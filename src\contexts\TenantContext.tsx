/* eslint-disable react-refresh/only-export-components */
import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import type { Company, User, TenantContextType } from '../types';
import { mockCompanies, mockUser } from '../data/mockData';

const TenantContext = createContext<TenantContextType | undefined>(undefined);

interface TenantProviderProps {
  children: ReactNode;
}

export const TenantProvider: React.FC<TenantProviderProps> = ({ children }) => {
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize data on mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsLoading(true);
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Load mock data
        setCompanies(mockCompanies);
        setUser(mockUser);
        
        // Set default company (first accessible company for the user)
        const accessibleCompanies = mockCompanies.filter(company => 
          mockUser.accessibleCompanies.includes(company.id)
        );
        
        if (accessibleCompanies.length > 0) {
          setCurrentCompany(accessibleCompanies[0]);
        }
        
        setError(null);
      } catch (err) {
        setError('Failed to load tenant data');
        console.error('Error initializing tenant data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  const switchCompany = (companyId: string) => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    // Check if user has access to this company
    if (!user.accessibleCompanies.includes(companyId)) {
      setError('Access denied to this company');
      return;
    }

    const company = companies.find(c => c.id === companyId);
    if (!company) {
      setError('Company not found');
      return;
    }

    setCurrentCompany(company);
    setError(null);
    
    // Store in localStorage for persistence
    localStorage.setItem('selectedCompanyId', companyId);
  };

  // Load saved company selection on mount
  useEffect(() => {
    const savedCompanyId = localStorage.getItem('selectedCompanyId');
    if (savedCompanyId && companies.length > 0 && user) {
      const savedCompany = companies.find(c => c.id === savedCompanyId);
      if (savedCompany && user.accessibleCompanies.includes(savedCompanyId)) {
        setCurrentCompany(savedCompany);
      }
    }
  }, [companies, user]);

  const contextValue: TenantContextType = {
    currentCompany,
    companies: companies.filter(company => 
      user?.accessibleCompanies.includes(company.id) || false
    ),
    user,
    switchCompany,
    isLoading,
    error
  };

  return (
    <TenantContext.Provider value={contextValue}>
      {children}
    </TenantContext.Provider>
  );
};

export const useTenant = (): TenantContextType => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

export default TenantContext;
