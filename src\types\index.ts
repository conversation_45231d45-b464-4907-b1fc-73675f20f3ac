/* eslint-disable @typescript-eslint/no-explicit-any */
// Company and Tenant Types
export interface Company {
  id: string;
  name: string;
  logo?: string;
  industry: string;
  foundedYear: number;
  employees: number;
  status: "active" | "inactive" | "pending";
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
  contact: {
    email: string;
    phone: string;
    website?: string;
  };
  settings: {
    currency: string;
    timezone: string;
    fiscalYearStart: string;
  };
}

// Financial Data Types
export interface FinancialData {
  companyId: string;
  period: string; // YYYY-MM format
  revenue: number;
  expenses: number;
  profit: number;
  cashFlow: number;
  assets: number;
  liabilities: number;
  equity: number;
}

// KPI Types
export interface KPI {
  id: string;
  name: string;
  value: number;
  previousValue: number;
  unit: string;
  trend: "up" | "down" | "stable";
  changePercentage: number;
  target?: number;
  category: "financial" | "operational" | "growth" | "efficiency";
}

// Dashboard Widget Types
export interface DashboardWidget {
  id: string;
  type: "kpi" | "chart" | "table" | "metric";
  title: string;
  size: "small" | "medium" | "large";
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  data: any;
  config: Record<string, any>;
}

// Chart Data Types
export interface ChartData {
  name: string;
  value: number;
  date?: string;
  category?: string;
  companyId?: string;
}

// User and Permission Types
export interface User {
  id: string;
  name: string;
  email: string;
  role: "admin" | "manager" | "viewer";
  avatar?: string;
  permissions: Permission[];
  accessibleCompanies: string[]; // Company IDs user can access
}

export interface Permission {
  resource: string;
  actions: ("read" | "write" | "delete" | "admin")[];
}

// Tenant Context Types
export interface TenantContextType {
  currentCompany: Company | null;
  companies: Company[];
  user: User | null;
  switchCompany: (companyId: string) => void;
  isLoading: boolean;
  error: string | null;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

// Dashboard State Types
export interface DashboardState {
  widgets: DashboardWidget[];
  layout: "grid" | "list";
  filters: {
    dateRange: [string, string];
    companies: string[];
    categories: string[];
  };
  isLoading: boolean;
}

// Navigation Types
export interface NavigationItem {
  key: string;
  label: string;
  icon: string;
  path: string;
  children?: NavigationItem[];
  permissions?: string[];
}

// Theme Types
export interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  mode: "light" | "dark";
  compactMode: boolean;
}
