/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Card, Typography, Space, Select } from 'antd';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';
import type { FinancialData } from '../../types';

const { Title, Text } = Typography;
const { Option } = Select;

interface RevenueChartProps {
  data: FinancialData[];
  title?: string;
  type?: 'line' | 'bar';
  height?: number;
  loading?: boolean;
}

const RevenueChart: React.FC<RevenueChartProps> = ({
  data,
  title = 'Receita Mensal',
  type = 'line',
  height = 300,
  loading = false
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPeriod = (period: string) => {
    const [year, month] = period.split('-');
    const monthNames = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ];
    return `${monthNames[parseInt(month) - 1]}/${year}`;
  };

  const chartData = data.map(item => ({
    period: formatPeriod(item.period),
    receita: item.revenue,
    despesas: item.expenses,
    lucro: item.profit,
    'Fluxo de Caixa': item.cashFlow
  }));

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          backgroundColor: 'white',
          padding: '12px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <Text strong>{label}</Text>
          {payload.map((entry: any, index: number) => (
            <div key={index} style={{ color: entry.color, marginTop: '4px' }}>
              <Text style={{ color: entry.color }}>
                {entry.name}: {formatCurrency(entry.value)}
              </Text>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    if (type === 'bar') {
      return (
        <BarChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="period" 
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            tickFormatter={formatCurrency}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Bar 
            dataKey="receita" 
            fill="#1890ff" 
            name="Receita"
            radius={[2, 2, 0, 0]}
          />
          <Bar 
            dataKey="despesas" 
            fill="#ff4d4f" 
            name="Despesas"
            radius={[2, 2, 0, 0]}
          />
          <Bar 
            dataKey="lucro" 
            fill="#52c41a" 
            name="Lucro"
            radius={[2, 2, 0, 0]}
          />
        </BarChart>
      );
    }

    return (
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="period" 
          tick={{ fontSize: 12 }}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="receita" 
          stroke="#1890ff" 
          strokeWidth={3}
          name="Receita"
          dot={{ fill: '#1890ff', strokeWidth: 2, r: 4 }}
        />
        <Line 
          type="monotone" 
          dataKey="despesas" 
          stroke="#ff4d4f" 
          strokeWidth={3}
          name="Despesas"
          dot={{ fill: '#ff4d4f', strokeWidth: 2, r: 4 }}
        />
        <Line 
          type="monotone" 
          dataKey="lucro" 
          stroke="#52c41a" 
          strokeWidth={3}
          name="Lucro"
          dot={{ fill: '#52c41a', strokeWidth: 2, r: 4 }}
        />
      </LineChart>
    );
  };

  return (
    <Card
      loading={loading}
      title={
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>
            {title}
          </Title>
          <Select
            defaultValue={type}
            size="small"
            style={{ width: 100 }}
          >
            <Option value="line">Linha</Option>
            <Option value="bar">Barras</Option>
          </Select>
        </Space>
      }
      style={{
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}
    >
      <ResponsiveContainer width="100%" height={height}>
        {renderChart()}
      </ResponsiveContainer>
      
      <div style={{ marginTop: '16px', textAlign: 'center' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          Dados financeiros dos últimos {data.length} meses
        </Text>
      </div>
    </Card>
  );
};

export default RevenueChart;
