/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }

  .ant-layout-sider-collapsed {
    margin-left: -200px;
  }

  .ant-layout-content {
    margin-left: 0 !important;
    margin: 16px !important;
    padding: 16px !important;
  }

  .ant-layout-header {
    padding: 0 16px !important;
  }

  .ant-card {
    margin-bottom: 16px;
  }

  .ant-statistic-title {
    font-size: 12px !important;
  }

  .ant-statistic-content {
    font-size: 18px !important;
  }
}

@media (max-width: 576px) {
  .ant-layout-content {
    margin: 8px !important;
    padding: 12px !important;
  }

  .ant-card-body {
    padding: 16px !important;
  }

  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-modal {
    margin: 0 !important;
    max-width: 100vw !important;
    width: 100vw !important;
    height: 100vh !important;
    top: 0 !important;
  }

  .ant-modal-content {
    height: 100vh;
    border-radius: 0;
  }

  .ant-modal-body {
    max-height: calc(100vh - 110px);
    overflow-y: auto;
  }
}

/* Custom Responsive Components */
.responsive-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media (max-width: 768px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.mobile-hidden {
  display: block;
}

@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block !important;
  }
}

/* Chart Responsiveness */
.recharts-wrapper {
  width: 100% !important;
}

@media (max-width: 768px) {
  .recharts-wrapper {
    height: 250px !important;
  }
}

/* Table Responsiveness */
@media (max-width: 768px) {
  .ant-table-thead > tr > th {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }

  .ant-table-pagination {
    margin: 16px 0 !important;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.full-width {
  width: 100%;
}
