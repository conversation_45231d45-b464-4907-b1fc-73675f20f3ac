import { useState } from 'react';
import { ConfigProvider } from 'antd';
import ptBR from 'antd/locale/pt_BR';
import { TenantProvider } from './contexts/TenantContext';
import LoginForm from './components/auth/LoginForm';
import DashboardLayout from './components/layout/DashboardLayout';
import Dashboard from './pages/Dashboard';
import './App.css';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loginLoading, setLoginLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  const handleLogin = async (credentials: { email: string; password: string }) => {
    setLoginLoading(true);
    setLoginError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Simple demo authentication
      if (credentials.email === '<EMAIL>' && credentials.password === 'demo123') {
        setIsAuthenticated(true);
      } else {
        throw new Error('Credenciais inválidas. Use: <EMAIL> / demo123');
      }
    } catch (error) {
      setLoginError(error instanceof Error ? error.message : 'Erro desconhecido');
    } finally {
      setLoginLoading(false);
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setLoginError(null);
  };

  if (!isAuthenticated) {
    return (
      <ConfigProvider locale={ptBR}>
        <LoginForm
          onLogin={handleLogin}
          loading={loginLoading}
          error={loginError}
        />
      </ConfigProvider>
    );
  }

  return (
    <ConfigProvider locale={ptBR}>
      <TenantProvider>
        <DashboardLayout onLogout={handleLogout}>
          <Dashboard />
        </DashboardLayout>
      </TenantProvider>
    </ConfigProvider>
  );
}

export default App;
