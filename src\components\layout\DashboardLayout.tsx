import React, { useState } from 'react';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Space,
  Typography,
  Button,
  Badge,
  Breadcrumb,
  theme
} from 'antd';
import {
  DashboardOutlined,
  BarChartOutlined,
  TeamOutlined,
  SettingOutlined,
  LogoutOutlined,
  UserOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BuildOutlined,
  FileTextOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { useTenant } from '../../contexts/TenantContext';
import TenantSelector from '../tenant/TenantSelector';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

interface DashboardLayoutProps {
  children: React.ReactNode;
  onLogout: () => void;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children, onLogout }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { currentCompany, user } = useTenant();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: 'analytics',
      icon: <BarChartOutlined />,
      label: 'Analytics',
      children: [
        { key: 'financial', label: 'Financeiro', icon: <DollarOutlined /> },
        { key: 'performance', label: 'Performance', icon: <BarChartOutlined /> },
        { key: 'trends', label: 'Tendências', icon: <FileTextOutlined /> },
      ],
    },
    {
      key: 'companies',
      icon: <BuildOutlined />,
      label: 'Empresas',
    },
    {
      key: 'team',
      icon: <TeamOutlined />,
      label: 'Equipe',
    },
    {
      key: 'reports',
      icon: <FileTextOutlined />,
      label: 'Relatórios',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Configurações',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Perfil',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Configurações',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Sair',
      onClick: onLogout,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: colorBgContainer,
          borderRight: '1px solid #f0f0f0'
        }}
      >
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          {!collapsed ? (
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              High Capital
            </Title>
          ) : (
            <Avatar style={{ backgroundColor: '#1890ff' }}>HC</Avatar>
          )}
        </div>
        
        <Menu
          mode="inline"
          defaultSelectedKeys={['dashboard']}
          items={menuItems}
          style={{ borderRight: 0, marginTop: 8 }}
        />
      </Sider>

      <Layout>
        <Header 
          style={{ 
            padding: '0 24px', 
            background: colorBgContainer,
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 64, height: 64 }}
            />
            
            {currentCompany && (
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Empresa Atual:
                </Text>
                <br />
                <Text strong style={{ fontSize: '14px' }}>
                  {currentCompany.name}
                </Text>
              </div>
            )}
          </Space>

          <Space size="large">
            <TenantSelector size="middle" />
            
            <Badge count={3}>
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                size="large"
              />
            </Badge>

            <Dropdown 
              menu={{ items: userMenuItems }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar 
                  src={user?.avatar} 
                  icon={<UserOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
                <div style={{ textAlign: 'left' }}>
                  <div style={{ fontSize: '14px', fontWeight: 500 }}>
                    {user?.name}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {user?.role === 'admin' ? 'Administrador' : 
                     user?.role === 'manager' ? 'Gerente' : 'Visualizador'}
                  </div>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        <Content
          style={{
            margin: '24px',
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          <div style={{ marginBottom: 16 }}>
            <Breadcrumb
              items={[
                { title: 'Home' },
                { title: 'Dashboard' },
              ]}
            />
          </div>
          
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default DashboardLayout;
