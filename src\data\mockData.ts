import type { Company, User, FinancialData, KPI } from "../types";

// Mock Companies Data
export const mockCompanies: Company[] = [
  {
    id: "comp-001",
    name: "TechCorp Solutions",
    logo: "/logos/techcorp.png",
    industry: "Technology",
    foundedYear: 2018,
    employees: 150,
    status: "active",
    address: {
      street: "123 Tech Street",
      city: "São Paulo",
      state: "SP",
      country: "Brazil",
      zipCode: "01234-567",
    },
    contact: {
      email: "<EMAIL>",
      phone: "+55 11 9999-8888",
      website: "https://techcorp.com",
    },
    settings: {
      currency: "BRL",
      timezone: "America/Sao_Paulo",
      fiscalYearStart: "01-01",
    },
  },
  {
    id: "comp-002",
    name: "Green Energy Ltd",
    logo: "/logos/greenenergy.png",
    industry: "Renewable Energy",
    foundedYear: 2020,
    employees: 85,
    status: "active",
    address: {
      street: "456 Solar Avenue",
      city: "Rio de Janeiro",
      state: "RJ",
      country: "Brazil",
      zipCode: "20000-123",
    },
    contact: {
      email: "<EMAIL>",
      phone: "+55 21 8888-7777",
      website: "https://greenenergy.com",
    },
    settings: {
      currency: "BRL",
      timezone: "America/Sao_Paulo",
      fiscalYearStart: "01-01",
    },
  },
  {
    id: "comp-003",
    name: "FinanceMax Consulting",
    logo: "/logos/financemax.png",
    industry: "Financial Services",
    foundedYear: 2015,
    employees: 220,
    status: "active",
    address: {
      street: "789 Finance Plaza",
      city: "Belo Horizonte",
      state: "MG",
      country: "Brazil",
      zipCode: "30000-456",
    },
    contact: {
      email: "<EMAIL>",
      phone: "+55 31 7777-6666",
      website: "https://financemax.com",
    },
    settings: {
      currency: "BRL",
      timezone: "America/Sao_Paulo",
      fiscalYearStart: "01-01",
    },
  },
  {
    id: "comp-004",
    name: "RetailPro Chain",
    logo: "/logos/retailpro.png",
    industry: "Retail",
    foundedYear: 2012,
    employees: 450,
    status: "active",
    address: {
      street: "321 Commerce Street",
      city: "Porto Alegre",
      state: "RS",
      country: "Brazil",
      zipCode: "90000-789",
    },
    contact: {
      email: "<EMAIL>",
      phone: "+55 51 6666-5555",
      website: "https://retailpro.com",
    },
    settings: {
      currency: "BRL",
      timezone: "America/Sao_Paulo",
      fiscalYearStart: "01-01",
    },
  },
  {
    id: "comp-005",
    name: "HealthCare Plus",
    logo: "/logos/healthcare.png",
    industry: "Healthcare",
    foundedYear: 2019,
    employees: 180,
    status: "active",
    address: {
      street: "654 Medical Center",
      city: "Salvador",
      state: "BA",
      country: "Brazil",
      zipCode: "40000-321",
    },
    contact: {
      email: "<EMAIL>",
      phone: "+55 71 5555-4444",
      website: "https://healthcareplus.com",
    },
    settings: {
      currency: "BRL",
      timezone: "America/Sao_Paulo",
      fiscalYearStart: "01-01",
    },
  },
];

// Mock User Data
export const mockUser: User = {
  id: "user-001",
  name: "João Silva",
  email: "<EMAIL>",
  role: "admin",
  avatar: "/avatars/joao.jpg",
  permissions: [
    { resource: "dashboard", actions: ["read", "write", "admin"] },
    { resource: "companies", actions: ["read", "write", "admin"] },
    { resource: "financial", actions: ["read", "write"] },
    { resource: "reports", actions: ["read", "write"] },
    { resource: "users", actions: ["read", "write", "admin"] },
  ],
  accessibleCompanies: [
    "comp-001",
    "comp-002",
    "comp-003",
    "comp-004",
    "comp-005",
  ],
};

// Mock Financial Data
export const mockFinancialData: FinancialData[] = [
  // TechCorp Solutions
  {
    companyId: "comp-001",
    period: "2024-01",
    revenue: 2500000,
    expenses: 1800000,
    profit: 700000,
    cashFlow: 650000,
    assets: 8500000,
    liabilities: 3200000,
    equity: 5300000,
  },
  {
    companyId: "comp-001",
    period: "2024-02",
    revenue: 2800000,
    expenses: 1900000,
    profit: 900000,
    cashFlow: 850000,
    assets: 8800000,
    liabilities: 3100000,
    equity: 5700000,
  },
  {
    companyId: "comp-001",
    period: "2024-03",
    revenue: 3200000,
    expenses: 2100000,
    profit: 1100000,
    cashFlow: 1050000,
    assets: 9200000,
    liabilities: 3000000,
    equity: 6200000,
  },

  // Green Energy Ltd
  {
    companyId: "comp-002",
    period: "2024-01",
    revenue: 1800000,
    expenses: 1400000,
    profit: 400000,
    cashFlow: 380000,
    assets: 6200000,
    liabilities: 2800000,
    equity: 3400000,
  },
  {
    companyId: "comp-002",
    period: "2024-02",
    revenue: 2100000,
    expenses: 1500000,
    profit: 600000,
    cashFlow: 580000,
    assets: 6500000,
    liabilities: 2700000,
    equity: 3800000,
  },
  {
    companyId: "comp-002",
    period: "2024-03",
    revenue: 2400000,
    expenses: 1600000,
    profit: 800000,
    cashFlow: 750000,
    assets: 6800000,
    liabilities: 2600000,
    equity: 4200000,
  },

  // FinanceMax Consulting
  {
    companyId: "comp-003",
    period: "2024-01",
    revenue: 3500000,
    expenses: 2200000,
    profit: 1300000,
    cashFlow: 1250000,
    assets: 12000000,
    liabilities: 4500000,
    equity: 7500000,
  },
  {
    companyId: "comp-003",
    period: "2024-02",
    revenue: 3800000,
    expenses: 2300000,
    profit: 1500000,
    cashFlow: 1450000,
    assets: 12500000,
    liabilities: 4400000,
    equity: 8100000,
  },
  {
    companyId: "comp-003",
    period: "2024-03",
    revenue: 4200000,
    expenses: 2500000,
    profit: 1700000,
    cashFlow: 1650000,
    assets: 13000000,
    liabilities: 4300000,
    equity: 8700000,
  },

  // RetailPro Chain
  {
    companyId: "comp-004",
    period: "2024-01",
    revenue: 5200000,
    expenses: 3800000,
    profit: 1400000,
    cashFlow: 1350000,
    assets: 18000000,
    liabilities: 8500000,
    equity: 9500000,
  },
  {
    companyId: "comp-004",
    period: "2024-02",
    revenue: 5500000,
    expenses: 4000000,
    profit: 1500000,
    cashFlow: 1450000,
    assets: 18500000,
    liabilities: 8300000,
    equity: 10200000,
  },
  {
    companyId: "comp-004",
    period: "2024-03",
    revenue: 5800000,
    expenses: 4200000,
    profit: 1600000,
    cashFlow: 1550000,
    assets: 19000000,
    liabilities: 8100000,
    equity: 10900000,
  },

  // HealthCare Plus
  {
    companyId: "comp-005",
    period: "2024-01",
    revenue: 3400000,
    expenses: 2400000,
    profit: 1000000,
    cashFlow: 950000,
    assets: 14000000,
    liabilities: 6200000,
    equity: 7800000,
  },
  {
    companyId: "comp-005",
    period: "2024-02",
    revenue: 3600000,
    expenses: 2500000,
    profit: 1100000,
    cashFlow: 1050000,
    assets: 14500000,
    liabilities: 6000000,
    equity: 8500000,
  },
  {
    companyId: "comp-005",
    period: "2024-03",
    revenue: 3800000,
    expenses: 2600000,
    profit: 1200000,
    cashFlow: 1150000,
    assets: 15000000,
    liabilities: 5800000,
    equity: 9200000,
  },
];

// Mock KPIs
export const mockKPIs: Record<string, KPI[]> = {
  "comp-001": [
    {
      id: "kpi-1",
      name: "Receita Mensal",
      value: 3200000,
      previousValue: 2800000,
      unit: "BRL",
      trend: "up",
      changePercentage: 14.3,
      target: 3500000,
      category: "financial",
    },
    {
      id: "kpi-2",
      name: "Margem de Lucro",
      value: 34.4,
      previousValue: 32.1,
      unit: "%",
      trend: "up",
      changePercentage: 7.2,
      target: 35,
      category: "financial",
    },
    {
      id: "kpi-3",
      name: "Funcionários",
      value: 150,
      previousValue: 145,
      unit: "people",
      trend: "up",
      changePercentage: 3.4,
      category: "operational",
    },
    {
      id: "kpi-4",
      name: "Satisfação Cliente",
      value: 4.7,
      previousValue: 4.5,
      unit: "/5",
      trend: "up",
      changePercentage: 4.4,
      target: 4.8,
      category: "operational",
    },
  ],
  "comp-002": [
    {
      id: "kpi-1",
      name: "Receita Mensal",
      value: 2400000,
      previousValue: 2100000,
      unit: "BRL",
      trend: "up",
      changePercentage: 14.3,
      target: 2800000,
      category: "financial",
    },
    {
      id: "kpi-2",
      name: "Margem de Lucro",
      value: 33.3,
      previousValue: 28.6,
      unit: "%",
      trend: "up",
      changePercentage: 16.4,
      target: 35,
      category: "financial",
    },
    {
      id: "kpi-3",
      name: "Funcionários",
      value: 85,
      previousValue: 82,
      unit: "people",
      trend: "up",
      changePercentage: 3.7,
      category: "operational",
    },
    {
      id: "kpi-4",
      name: "Projetos Verdes",
      value: 12,
      previousValue: 10,
      unit: "projetos",
      trend: "up",
      changePercentage: 20,
      target: 15,
      category: "growth",
    },
  ],
  "comp-003": [
    {
      id: "kpi-1",
      name: "Receita Mensal",
      value: 4200000,
      previousValue: 3800000,
      unit: "BRL",
      trend: "up",
      changePercentage: 10.5,
      target: 4500000,
      category: "financial",
    },
    {
      id: "kpi-2",
      name: "Margem de Lucro",
      value: 40.5,
      previousValue: 39.5,
      unit: "%",
      trend: "up",
      changePercentage: 2.5,
      target: 42,
      category: "financial",
    },
    {
      id: "kpi-3",
      name: "Funcionários",
      value: 220,
      previousValue: 215,
      unit: "people",
      trend: "up",
      changePercentage: 2.3,
      category: "operational",
    },
    {
      id: "kpi-4",
      name: "Clientes Ativos",
      value: 450,
      previousValue: 420,
      unit: "clientes",
      trend: "up",
      changePercentage: 7.1,
      target: 500,
      category: "growth",
    },
  ],
  "comp-004": [
    {
      id: "kpi-1",
      name: "Receita Mensal",
      value: 5800000,
      previousValue: 5200000,
      unit: "BRL",
      trend: "up",
      changePercentage: 11.5,
      target: 6200000,
      category: "financial",
    },
    {
      id: "kpi-2",
      name: "Margem de Lucro",
      value: 28.2,
      previousValue: 26.8,
      unit: "%",
      trend: "up",
      changePercentage: 5.2,
      target: 30,
      category: "financial",
    },
    {
      id: "kpi-3",
      name: "Funcionários",
      value: 450,
      previousValue: 445,
      unit: "people",
      trend: "up",
      changePercentage: 1.1,
      category: "operational",
    },
    {
      id: "kpi-4",
      name: "Lojas Ativas",
      value: 28,
      previousValue: 26,
      unit: "lojas",
      trend: "up",
      changePercentage: 7.7,
      target: 32,
      category: "growth",
    },
  ],
  "comp-005": [
    {
      id: "kpi-1",
      name: "Receita Mensal",
      value: 3800000,
      previousValue: 3400000,
      unit: "BRL",
      trend: "up",
      changePercentage: 11.8,
      target: 4100000,
      category: "financial",
    },
    {
      id: "kpi-2",
      name: "Margem de Lucro",
      value: 31.5,
      previousValue: 29.2,
      unit: "%",
      trend: "up",
      changePercentage: 7.9,
      target: 33,
      category: "financial",
    },
    {
      id: "kpi-3",
      name: "Funcionários",
      value: 180,
      previousValue: 175,
      unit: "people",
      trend: "up",
      changePercentage: 2.9,
      category: "operational",
    },
    {
      id: "kpi-4",
      name: "Pacientes Atendidos",
      value: 1250,
      previousValue: 1180,
      unit: "pacientes",
      trend: "up",
      changePercentage: 5.9,
      target: 1400,
      category: "operational",
    },
  ],
};
