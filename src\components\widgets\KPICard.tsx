import React from 'react';
import { Card, Statistic, Space, Typography, Progress, Tag } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  DollarOutlined,
  TeamOutlined,
  TrophyOutlined,
  RiseOutlined
} from '@ant-design/icons';
import type { KPI } from '../../types';

const { Text } = Typography;

interface KPICardProps {
  kpi: KPI;
  loading?: boolean;
  style?: React.CSSProperties;
}

const KPICard: React.FC<KPICardProps> = ({ kpi, loading = false, style }) => {
  const getTrendIcon = () => {
    switch (kpi.trend) {
      case 'up':
        return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
      case 'down':
        return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <MinusOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getTrendColor = () => {
    switch (kpi.trend) {
      case 'up':
        return '#52c41a';
      case 'down':
        return '#ff4d4f';
      default:
        return '#faad14';
    }
  };

  const getCategoryIcon = () => {
    switch (kpi.category) {
      case 'financial':
        return <DollarOutlined style={{ color: '#1890ff' }} />;
      case 'operational':
        return <TeamOutlined style={{ color: '#722ed1' }} />;
      case 'growth':
        return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'efficiency':
        return <TrophyOutlined style={{ color: '#faad14' }} />;
      default:
        return <DollarOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getCategoryColor = () => {
    switch (kpi.category) {
      case 'financial':
        return 'blue';
      case 'operational':
        return 'purple';
      case 'growth':
        return 'green';
      case 'efficiency':
        return 'gold';
      default:
        return 'blue';
    }
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === 'BRL') {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    }
    
    if (unit === '%') {
      return `${value.toFixed(1)}%`;
    }
    
    if (unit === 'people') {
      return value.toString();
    }
    
    return `${value}${unit}`;
  };

  const getProgressPercent = () => {
    if (!kpi.target) return undefined;
    return Math.min((kpi.value / kpi.target) * 100, 100);
  };

  const getProgressStatus = () => {
    if (!kpi.target) return undefined;
    const percent = (kpi.value / kpi.target) * 100;
    if (percent >= 100) return 'success';
    if (percent >= 80) return 'normal';
    return 'exception';
  };

  return (
    <Card
      loading={loading}
      style={{
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        ...style
      }}
      bodyStyle={{ padding: '20px' }}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* Header with category and icon */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Tag color={getCategoryColor()} icon={getCategoryIcon()}>
            {kpi.category === 'financial' ? 'Financeiro' :
             kpi.category === 'operational' ? 'Operacional' :
             kpi.category === 'growth' ? 'Crescimento' : 'Eficiência'}
          </Tag>
          {getTrendIcon()}
        </div>

        {/* Main KPI Value */}
        <Statistic
          title={kpi.name}
          value={formatValue(kpi.value, kpi.unit)}
          valueStyle={{ 
            fontSize: '24px', 
            fontWeight: 'bold',
            color: '#262626'
          }}
        />

        {/* Change indicator */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Text 
              style={{ 
                color: getTrendColor(),
                fontSize: '14px',
                fontWeight: 500
              }}
            >
              {kpi.changePercentage > 0 ? '+' : ''}{kpi.changePercentage.toFixed(1)}%
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              vs período anterior
            </Text>
          </Space>
        </div>

        {/* Previous value comparison */}
        <div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Anterior: {formatValue(kpi.previousValue, kpi.unit)}
          </Text>
        </div>

        {/* Progress bar for targets */}
        {kpi.target && (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <Text style={{ fontSize: '12px' }}>Meta</Text>
              <Text style={{ fontSize: '12px' }}>
                {formatValue(kpi.target, kpi.unit)}
              </Text>
            </div>
            <Progress
              percent={getProgressPercent()}
              status={getProgressStatus()}
              size="small"
              showInfo={false}
            />
          </div>
        )}
      </Space>
    </Card>
  );
};

export default KPICard;
