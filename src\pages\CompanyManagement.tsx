import React, { useState } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Statistic,
  Avatar,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  BuildOutlined,
  UserOutlined,
  DollarOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTenant } from '../contexts/TenantContext';
import { type Company } from '../types';
import { mockFinancialData } from '../data/mockData';

const { Title, Text } = Typography;
const { Option } = Select;

const CompanyManagement: React.FC = () => {
  const { companies } = useTenant();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCompany, setEditingCompany] = useState<Company | null>(null);
  const [form] = Form.useForm();

  const getIndustryColor = (industry: string): string => {
    const colors: Record<string, string> = {
      'Technology': 'blue',
      'Renewable Energy': 'green',
      'Financial Services': 'gold',
      'Retail': 'purple',
      'Healthcare': 'red',
      'Manufacturing': 'orange',
      'Education': 'cyan'
    };
    return colors[industry] || 'default';
  };

  const getLatestRevenue = (companyId: string): number => {
    const companyData = mockFinancialData
      .filter(data => data.companyId === companyId)
      .sort((a, b) => b.period.localeCompare(a.period));
    return companyData.length > 0 ? companyData[0].revenue : 0;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const handleAdd = () => {
    setEditingCompany(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (company: Company) => {
    setEditingCompany(company);
    form.setFieldsValue({
      ...company,
      street: company.address.street,
      city: company.address.city,
      state: company.address.state,
      country: company.address.country,
      zipCode: company.address.zipCode,
      email: company.contact.email,
      phone: company.contact.phone,
      website: company.contact.website,
      currency: company.settings.currency,
      timezone: company.settings.timezone,
      fiscalYearStart: company.settings.fiscalYearStart
    });
    setIsModalVisible(true);
  };

  const handleDelete = (companyId: string) => {
    // In a real app, this would call an API
    console.log('Delete company:', companyId);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      // In a real app, this would call an API to save/update the company
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const columns = [
    {
      title: 'Empresa',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Company) => (
        <Space>
          <Avatar 
            icon={<BuildOutlined />}
            style={{ backgroundColor: '#1890ff' }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{text}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.address.city}, {record.address.state}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Setor',
      dataIndex: 'industry',
      key: 'industry',
      render: (industry: string) => (
        <Tag color={getIndustryColor(industry)}>{industry}</Tag>
      ),
    },
    {
      title: 'Funcionários',
      dataIndex: 'employees',
      key: 'employees',
      render: (employees: number) => (
        <Space>
          <UserOutlined />
          {employees}
        </Space>
      ),
    },
    {
      title: 'Receita Atual',
      key: 'revenue',
      render: (record: Company) => (
        <Space>
          <DollarOutlined />
          {formatCurrency(getLatestRevenue(record.id))}
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'orange'}>
          {status === 'active' ? 'Ativa' : 'Inativa'}
        </Tag>
      ),
    },
    {
      title: 'Fundada',
      dataIndex: 'foundedYear',
      key: 'foundedYear',
    },
    {
      title: 'Ações',
      key: 'actions',
      render: (record: Company) => (
        <Space>
          <Tooltip title="Visualizar">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
            />
          </Tooltip>
          <Tooltip title="Editar">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Tem certeza que deseja excluir esta empresa?"
            onConfirm={() => handleDelete(record.id)}
            okText="Sim"
            cancelText="Não"
          >
            <Tooltip title="Excluir">
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Calculate summary statistics
  const totalCompanies = companies.length;
  const totalEmployees = companies.reduce((sum, company) => sum + company.employees, 0);
  const totalRevenue = companies.reduce((sum, company) => sum + getLatestRevenue(company.id), 0);
  const activeCompanies = companies.filter(company => company.status === 'active').length;

  return (
    <div>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          Gerenciamento de Empresas
        </Title>
        <Text type="secondary">
          Gerencie todas as empresas do grupo High Capital
        </Text>
      </div>

      {/* Summary Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total de Empresas"
              value={totalCompanies}
              prefix={<BuildOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Empresas Ativas"
              value={activeCompanies}
              prefix={<BuildOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total de Funcionários"
              value={totalEmployees}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Receita Total"
              value={totalRevenue}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#faad14' }}
              formatter={(value) => formatCurrency(Number(value))}
            />
          </Card>
        </Col>
      </Row>

      {/* Companies Table */}
      <Card
        title="Lista de Empresas"
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            Nova Empresa
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={companies}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} de ${total} empresas`,
          }}
        />
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        title={editingCompany ? 'Editar Empresa' : 'Nova Empresa'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText="Salvar"
        cancelText="Cancelar"
      >
        <Form
          form={form}
          layout="vertical"
          name="companyForm"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Nome da Empresa"
                rules={[{ required: true, message: 'Nome é obrigatório' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="industry"
                label="Setor"
                rules={[{ required: true, message: 'Setor é obrigatório' }]}
              >
                <Select>
                  <Option value="Technology">Tecnologia</Option>
                  <Option value="Renewable Energy">Energia Renovável</Option>
                  <Option value="Financial Services">Serviços Financeiros</Option>
                  <Option value="Retail">Varejo</Option>
                  <Option value="Healthcare">Saúde</Option>
                  <Option value="Manufacturing">Manufatura</Option>
                  <Option value="Education">Educação</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="foundedYear"
                label="Ano de Fundação"
                rules={[{ required: true, message: 'Ano é obrigatório' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="employees"
                label="Funcionários"
                rules={[{ required: true, message: 'Número de funcionários é obrigatório' }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Status é obrigatório' }]}
              >
                <Select>
                  <Option value="active">Ativa</Option>
                  <Option value="inactive">Inativa</Option>
                  <Option value="pending">Pendente</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Title level={5}>Endereço</Title>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="street"
                label="Endereço"
                rules={[{ required: true, message: 'Endereço é obrigatório' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="city"
                label="Cidade"
                rules={[{ required: true, message: 'Cidade é obrigatória' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="state"
                label="Estado"
                rules={[{ required: true, message: 'Estado é obrigatório' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="zipCode"
                label="CEP"
                rules={[{ required: true, message: 'CEP é obrigatório' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Title level={5}>Contato</Title>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Email é obrigatório' },
                  { type: 'email', message: 'Email inválido' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="phone"
                label="Telefone"
                rules={[{ required: true, message: 'Telefone é obrigatório' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="website"
                label="Website"
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default CompanyManagement;
